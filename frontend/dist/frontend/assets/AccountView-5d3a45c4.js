import{G as he,q as be,s as we,a as ke,r as b,H as Y,c as de,o as De,v as f,b as j,d as w,e as n,f as r,g as t,h as d,w as p,C as D,I as q,F as z,i as K,B as Ce,E,z as ze,t as i,p as c,J as Ae,j as _,K as ue,L as Se,u as ie,M as F}from"./index-971af9ba.js";const Te={class:"container m-auto"},Ie={class:"flex w-full md:w-2/3 lg:w-1/2 m-auto justify-center items-center my-10 gap-4"},Me={class:"block md:hidden px-5"},$e=["onClick"],je={class:"text-nowrap"},Ue={key:1,class:"text-center py-8 border rounded-lg"},Oe={class:"hidden md:block"},Pe={class:"w-full"},Ne=["onClick"],Ve={class:"text-center"},qe={class:"text-center"},Ee={class:""},Le={class:"text-center"},Re={class:"text-center"},Be={class:"text-nowrap"},Je={class:"text-center"},Ye={class:"text-center"},Ke={key:1},Fe={key:0,class:"text-red-500 text-xs ml-1"},Qe={key:0,class:"text-red-500 text-xs ml-1"},Ge={key:0,class:"text-gray-400 text-xs ml-1"},He={key:0,class:"text-gray-400 text-xs ml-1"},Ze={key:0,class:"text-gray-400 text-xs ml-1"},We={key:0,class:"text-gray-400 text-xs ml-1"},Xe={key:0,class:"text-gray-400 text-xs ml-1"},et={key:0,class:"text-sm text-gray-500 mt-1"},tt={class:"flex justify-end"},st={key:0,type:"button",class:"bg-red-500 text-white w-1/6 p-2 mt-4"},ot={class:"col-span-2 md:col-span-1"},at={class:"col-span-2 md:col-span-1"},lt={key:0,class:"text-red-500 text-sm mt-1"},nt={key:1,class:"text-gray-500 text-xs mt-1"},rt={class:"col-span-2"},dt={key:0,class:"text-center py-8"},ut={key:1,class:"text-center py-8 border rounded-lg"},it={key:2,class:"border rounded-lg",style:{height:"720px",position:"relative"}},ct={class:"p-4 overflow-y-auto",style:{height:"calc(100% - 60px)","padding-bottom":"70px"}},pt={key:0,class:"flex items-start justify-end"},vt={class:"mr-2 text-right"},mt={class:"text-xs text-gray-400 mb-1"},gt={class:"bg-blue-100 rounded-lg px-4 py-2 text-blue-800 max-w-xs"},ft={class:"text-xs text-gray-500 mt-1"},yt={key:1,class:"flex items-start"},xt={class:"ml-2"},_t={class:"text-xs text-gray-400 mb-1"},ht={class:"bg-gray-100 rounded-lg px-4 py-2 text-gray-800 max-w-xs"},bt={class:"absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-3 rounded-b-lg"},wt={class:"flex"},kt={class:"mt-4"},Dt={key:0,class:"hidden md:flex justify-center"},Ct={key:1,class:"block md:hidden flex justify-center"},zt={class:"flex justify-end mt-4 space-x-2"},At=["disabled"],St={key:0},Tt={key:1},Ot={__name:"AccountView",setup(It){const A=he(),ce=be(),{clientData:h,isUser:Q,carts:Mt}=we(ce),{patchApiData:G,getApiData:H,delApiData:$t,cancelRequests:jt,postApiData:Z,checkAuthToken:pe,logApiError:ve}=ke(),k=b("order"),y=b({}),v=b(null),a=Y({open:!1,data:{}}),u=Y({current:1,pageSize:5,total:0,showSizeChanger:!0,showQuickJumper:!0,pageSizeOptions:["5","10","20","50"],showTotal:(s,e)=>`第 ${e[0]}-${e[1]} 項，共 ${s} 筆訂單`}),W=de(()=>{if(!v.value||!Array.isArray(v.value))return[];const s=(u.current-1)*u.pageSize,e=s+u.pageSize;return v.value.slice(s,e)}),X=(s,e)=>{u.current=s,u.pageSize=e},ee=(s,e)=>{u.current=1,u.pageSize=e},C=b([]),U=b(!1),S=Y({phone:""}),te=s=>{if(!s)return"聯絡電話為必填項目";const e=s.replace(/[^\d+]/g,""),m=/^09\d{8}$/,x=/^0[2-8]-?\d{7,8}$/,g=/^\+886\d{9}$/;return m.test(e)||x.test(s)||g.test(e)?"":"請輸入正確的電話號碼格式（例：09******** 或 02-********）"},se=()=>{S.phone=te(y.value.phone)},O=async()=>{if(U.value=!0,!pe()){U.value=!1;return}try{console.log("正在獲取用戶留言...");const s=await H("user/comments");console.log("獲取留言響應:",s),s&&s.resData?(C.value=s.resData,console.log("成功獲取用戶留言:",C.value.length,"條"),C.value.length>0&&console.log("留言示例:",C.value[0])):console.error("獲取留言失敗:",s==null?void 0:s.error)}catch(s){ve("user/comments",s)}finally{U.value=!1}},I=b(!1),T=b(""),P=b(!1),me=()=>{I.value=!0,T.value=""},ge=async()=>{if(!T.value.trim()){f.warning("留言內容不能為空");return}P.value=!0;try{const s=await Z("comments",{content:T.value});s&&s.status===201?(f.success("留言發送成功"),I.value=!1,T.value="",O()):f.error((s==null?void 0:s.error)||"留言發送失敗，請稍後再試")}catch(s){console.error("提交留言時發生錯誤:",s),f.error("留言發送失敗，請稍後再試")}finally{P.value=!1}};De(async()=>{var e,m,x,g,N;if(!Q.value){console.log("用戶未登入，重定向到登入頁面"),localStorage.setItem("redirect_after_login",window.location.href),A.push("/login");return}const s=A.currentRoute.value.query.tab;if(s&&(s==="userData"?k.value="userData":s==="comments"?(k.value="comments",O()):k.value="order"),y.value={...h.value},console.log("=== 開始獲取用戶訂單資料 ==="),console.log("用戶登入狀態:",Q.value),console.log("用戶資料:",h.value),console.log("用戶ID:",(e=h.value)==null?void 0:e.id),console.log("Local Storage Token:",localStorage.getItem("token")?"Token 存在":"Token 不存在"),h.value&&h.value.id)try{console.log("準備呼叫會員訂單API...");const l=await H("member/orders");if(console.log("會員訂單API完整響應:",l),console.log("響應中的 success:",l==null?void 0:l.success),console.log("響應中的 resData:",l==null?void 0:l.resData),console.log("響應中的 resTotal:",l==null?void 0:l.resTotal),console.log("響應中的 resTotalMoney:",l==null?void 0:l.resTotalMoney),l&&l.resData&&Array.isArray(l.resData)){console.log("訂單API原始數據樣本:",l.resData[0]||{}),console.log("訂單API數據結構:",Object.keys(l.resData[0]||{}));const V=Array.isArray(l.resData)?l.resData:[l.resData];v.value=V.map(J=>oe(J)),u.total=v.value.length,console.log("處理後的訂單數據樣本:",v.value[0]||{}),console.log("獲取到訂單數據:",v.value.length,"筆")}else l&&l.resData&&!Array.isArray(l.resData)?(console.log("API 返回的不是陣列，嘗試包裝為陣列"),v.value=[oe(l.resData)],u.total=v.value.length):(console.error("獲取訂單數據失敗或數據格式不正確:",{resData:l==null?void 0:l.resData,resTotal:l==null?void 0:l.resTotal,resTotalMoney:l==null?void 0:l.resTotalMoney,success:l==null?void 0:l.success,error:l==null?void 0:l.error}),v.value=[],u.total=0,l!=null&&l.error&&l.error.includes("401")&&(f.warning("登入已過期，請重新登入"),A.push("/login")))}catch(l){console.error("獲取訂單數據出錯:",l),console.error("錯誤詳細資訊:",{message:l.message,response:(m=l.response)==null?void 0:m.data,status:(x=l.response)==null?void 0:x.status}),v.value=[],u.total=0,((g=l.response)==null?void 0:g.status)===401?(f.warning("登入已過期，請重新登入"),A.push("/login")):((N=l.response)==null?void 0:N.status)===500?f.error("伺服器錯誤，請稍後再試"):f.error("獲取訂單資料失敗，請稍後再試")}else console.error("用戶未登入或用戶ID未定義，無法獲取訂單"),v.value=[],u.total=0,f.warning("請先登入"),A.push("/login");console.log("=== 訂單資料獲取完成 ===")});const oe=s=>{if(!s)return s;const e={...s};if(e.products)if(typeof e.products=="string")try{e.products=JSON.parse(e.products)}catch(m){console.error("解析訂單產品數據失敗:",m),e.products=[]}else Array.isArray(e.products)||(e.products=[]);else e.products=[];return e.receiver=e.receiver||"",e.phone=e.phone||"",e.address=e.address||"自取/無須配送",e.status=e.status||1,e.total=e.total||0,!e.date&&!e.createdAt&&(e.createdAt=new Date().toISOString()),e},ae=s=>{const e={...s};if(typeof e.products=="string")try{e.products=JSON.parse(e.products)}catch(m){console.error("解析訂單產品數據失敗:",m),e.products=[]}e.receiver=e.receiver||"",e.phone=e.phone||"",e.address=e.address||"自取/無須配送",console.log("訂單詳情 - 訂購人:",e.receiver),console.log("訂單詳情 - 聯絡電話:",e.phone),a.data=e,a.open=!0},fe=async()=>{await G("orderList",{status:4,id:a.data.id});const s=j.findIndex(v.value,e=>a.data.id==e.id);v.value[s].status=4,a.open=!1},le=j.debounce(async()=>{if(S.phone=te(y.value.phone),S.phone){f.error("請修正表單錯誤後再提交");return}console.log(j.isEqual(y.value,h.value));const s=j.omitBy(y.value,(e,m)=>j.isEqual(e,h.value[m]));if(console.log("diff",s),Object.keys(s).length===0){f.info("沒有需要更新的資料");return}try{const e=await G("user",{...s,id:h.value.id});e&&e.status===200?(f.success("修改成功"),h.value=y.value,setTimeout(()=>{window.location.href="/"},1e3)):f.error((e==null?void 0:e.error)||"發生錯誤，請稍後再試")}catch(e){console.error("更新資料時發生錯誤:",e),f.error("更新資料發生錯誤，請稍後再試")}},500),L=s=>s===1?"已付款":"待付款",R=s=>s===1?"green":"orange",M=s=>{if(!s)return"-";let e;return typeof s=="string"?s.includes("T")||s.includes("Z")?e=F(s):e=F(s):e=F(s),e.isValid()?e.format("YYYY年M月D日 A h:mm"):s},B=s=>{k.value=s,A.push({query:{tab:s}}),s==="comments"&&O()},ye=de(()=>{if(!C.value)return[];const s=[];return C.value.forEach(e=>{s.push({id:`user-${e.id}`,type:"user",content:e.content,time:new Date(e.createdAt),originalId:e.id,commentType:e.product?"商品留言":"一般留言",product:e.productName||null,productId:e.product||null}),e.reply&&s.push({id:`admin-${e.id}`,type:"admin",content:e.reply,time:new Date(e.replyAt||e.updatedAt||e.createdAt),originalId:e.id,commentType:e.product?"商品留言":"一般留言",product:e.productName||null,productId:e.product||null})}),s.sort((e,m)=>e.time-m.time)}),$=b(""),ne=async()=>{if($.value.trim())try{const s=await Z("comments",{content:$.value});s&&s.status===201&&($.value="",O())}catch(s){console.error("提交留言時發生錯誤:",s),f.error("留言發送失敗，請稍後再試")}};return(s,e)=>{const m=w("a-button"),x=w("a-tag"),g=w("a-descriptions-item"),N=w("a-descriptions"),l=w("a-popconfirm"),V=w("a-modal"),J=w("a-spin"),re=w("a-pagination"),xe=w("a-textarea");return n(),r("div",Te,[t("div",Ie,[d(m,{onClick:e[0]||(e[0]=o=>B("order")),size:"large",class:"flex-1 font-semibold text-base"},{default:p(()=>e[16]||(e[16]=[c("我的訂單")])),_:1,__:[16]}),d(m,{onClick:e[1]||(e[1]=o=>B("userData")),size:"large",class:"flex-1 font-semibold text-base"},{default:p(()=>e[17]||(e[17]=[c("個人資料")])),_:1,__:[17]}),d(m,{onClick:e[2]||(e[2]=o=>B("comments")),size:"large",class:"flex-1 font-semibold text-base"},{default:p(()=>e[18]||(e[18]=[c(" 留言板 ")])),_:1,__:[18]})]),t("div",null,[D(t("div",null,[t("div",Me,[v.value&&v.value.length>0?(n(!0),r(z,{key:0},K(W.value,o=>(n(),r("div",{class:"border-2 w-full p-4 mb-4",key:o.id,onClick:_e=>ae(o)},[t("p",null,[e[19]||(e[19]=t("span",{class:"mr-4"},"訂單時間",-1)),c(i(M(o.date||o.createdAt)),1)]),t("p",null,[e[20]||(e[20]=t("span",{class:"mr-4"},"訂單編號",-1)),c(i(o.orderId),1)]),t("p",null,[e[21]||(e[21]=t("span",{class:"mr-4"},"商品數量",-1)),c(i(o.products?o.products.length:0),1)]),t("p",je,[e[22]||(e[22]=t("span",{class:"mr-4"},"購買金額",-1)),e[23]||(e[23]=c("NT ")),d(ue,{number:o.total},null,8,["number"])]),t("p",null,[e[24]||(e[24]=t("span",{class:"mr-4"},"付款狀態",-1)),d(x,{color:R(o.payment_status)},{default:p(()=>[c(i(L(o.payment_status)),1)]),_:2},1032,["color"])]),t("p",null,[e[25]||(e[25]=t("span",{class:"mr-4"},"訂單狀態",-1)),d(x,{color:o.status==1?"red":o.status==2?"green":o.status==3?"blue":"gray"},{default:p(()=>[c(i(o.status==1?"處理中":o.status==2?"已出貨":o.status==3?"已完成":"已取消"),1)]),_:2},1032,["color"])])],8,$e))),128)):(n(),r("div",Ue,e[26]||(e[26]=[t("p",{class:"text-gray-500"},"您目前沒有任何訂單",-1)])))]),t("div",Oe,[t("table",Pe,[e[29]||(e[29]=t("thead",null,[t("tr",null,[t("th",{scope:"col"},"購買日期"),t("th",{scope:"col"},"訂單編號"),t("th",{scope:"col",class:""},"商品數量"),t("th",{scope:"col",class:""},"購買金額"),t("th",{scope:"col"},"付款狀態"),t("th",{scope:"col"},"訂單狀態")])],-1)),t("tbody",null,[v.value&&v.value.length>0?(n(!0),r(z,{key:0},K(W.value,o=>(n(),r("tr",{key:o.id,class:"hover:bg-slate-100 cursor-pointer",onClick:_e=>ae(o)},[t("td",Ve,[t("p",null,i(M(o.date||o.createdAt)),1)]),t("td",qe,[t("p",null,i(o.orderId),1)]),t("td",Ee,[t("p",Le,i(o.products?o.products.length:0),1)]),t("td",Re,[t("p",Be,[e[27]||(e[27]=c("NT ")),d(ue,{number:o.total},null,8,["number"])])]),t("td",Je,[d(x,{color:R(o.payment_status)},{default:p(()=>[c(i(L(o.payment_status)),1)]),_:2},1032,["color"])]),t("td",Ye,[d(x,{color:o.status==1?"red":o.status==2?"green":o.status==3?"blue":"gray"},{default:p(()=>[c(i(o.status==1?"處理中":o.status==2?"已出貨":o.status==3?"已完成":"已取消"),1)]),_:2},1032,["color"])])],8,Ne))),128)):(n(),r("tr",Ke,e[28]||(e[28]=[t("td",{colspan:"6",class:"text-center py-8"},[t("p",{class:"text-gray-500"},"您目前沒有任何訂單")],-1)])))])])]),d(V,{open:a.open,"onUpdate:open":e[3]||(e[3]=o=>a.open=o),title:a.data.orderId+"訂單內容",footer:null,width:"1200px"},{default:p(()=>[d(Se,{cartData:a.data.products,orderTotal:a.data.total},null,8,["cartData","orderTotal"]),d(N,{title:"訂單資料"},{default:p(()=>[d(g,{label:"訂購時間"},{default:p(()=>[c(i(M(a.data.date||a.data.createdAt)),1)]),_:1}),d(g,{label:"訂購人"},{default:p(()=>[c(i(a.data.receiver||"未提供")+" ",1),a.data.receiver?_("",!0):(n(),r("span",Fe,"(資料未填寫)"))]),_:1}),d(g,{label:"聯絡電話"},{default:p(()=>[c(i(a.data.phone||"未提供")+" ",1),a.data.phone?_("",!0):(n(),r("span",Qe,"(資料未填寫)"))]),_:1}),d(g,{label:"住址（詳細地址）"},{default:p(()=>[c(i(a.data.detailed_address&&a.data.detailed_address!=="/"?a.data.detailed_address:"未提供")+" ",1),!a.data.detailed_address||a.data.detailed_address==="/"?(n(),r("span",Ge,"(可選填)")):_("",!0)]),_:1}),d(g,{label:"儲互社"},{default:p(()=>[c(i(a.data.cooperative&&a.data.cooperative!=="/"?a.data.cooperative:"未提供")+" ",1),!a.data.cooperative||a.data.cooperative==="/"?(n(),r("span",He,"(可選填)")):_("",!0)]),_:1}),d(g,{label:"公司抬頭"},{default:p(()=>[c(i(a.data.invoice_title&&a.data.invoice_title!=="/"?a.data.invoice_title:"未提供")+" ",1),!a.data.invoice_title||a.data.invoice_title==="/"?(n(),r("span",Ze,"(可選填)")):_("",!0)]),_:1}),d(g,{label:"統編"},{default:p(()=>[c(i(a.data.tax_id&&a.data.tax_id!=="/"?a.data.tax_id:"未提供")+" ",1),!a.data.tax_id||a.data.tax_id==="/"?(n(),r("span",We,"(可選填)")):_("",!0)]),_:1}),d(g,{label:"備註"},{default:p(()=>[c(i(a.data.notes&&a.data.notes!=="/"?a.data.notes:"無")+" ",1),!a.data.notes||a.data.notes==="/"?(n(),r("span",Xe,"(可選填)")):_("",!0)]),_:1}),d(g,{label:"付款狀態"},{default:p(()=>[d(x,{color:R(a.data.payment_status)},{default:p(()=>[c(i(L(a.data.payment_status)),1)]),_:1},8,["color"]),a.data.payment_status===1&&a.data.payment_date?(n(),r("p",et," 付款時間："+i(M(a.data.payment_date)),1)):_("",!0)]),_:1}),d(g,{label:"訂單狀態"},{default:p(()=>[d(x,{color:a.data.status==1?"red":a.data.status==2?"green":a.data.status==3?"blue":"gray"},{default:p(()=>[c(i(a.data.status==1?"處理中":a.data.status==2?"已出貨":a.data.status==3?"已完成":"已取消"),1)]),_:1},8,["color"])]),_:1})]),_:1}),t("div",tt,[d(l,{title:"確定取消訂單嗎？","ok-text":"確定","cancel-text":"取消",onConfirm:fe,class:"mt-1"},{default:p(()=>[a.data.status==1?(n(),r("button",st," 取消訂單 ")):_("",!0)]),_:1})])]),_:1},8,["open","title"])],512),[[q,k.value=="order"]]),D(t("div",null,[t("form",{onSubmit:e[7]||(e[7]=Ce((...o)=>ie(le)&&ie(le)(...o),["prevent"])),class:"grid grid-cols-2 gap-4 w-1/2 m-auto"},[t("div",ot,[e[30]||(e[30]=t("p",null,"姓名",-1)),D(t("input",{class:"w-full border-2 border-black",type:"text",placeholder:"請輸入姓名","onUpdate:modelValue":e[4]||(e[4]=o=>y.value.name=o)},null,512),[[E,y.value.name]])]),t("div",at,[e[31]||(e[31]=t("p",null,"聯絡電話",-1)),D(t("input",{class:ze(["w-full border-2",S.phone?"border-red-500":"border-black"]),type:"text",placeholder:"請輸入連絡電話","onUpdate:modelValue":e[5]||(e[5]=o=>y.value.phone=o),onBlur:se,onInput:se},null,34),[[E,y.value.phone]]),S.phone?(n(),r("p",lt,i(S.phone),1)):(n(),r("p",nt,"支援格式：手機 (09********)、市話 (02-********)"))]),t("div",rt,[e[32]||(e[32]=t("p",null,"生日",-1)),D(t("input",{class:"w-full border-2 border-black",type:"date",placeholder:"請選擇生日","onUpdate:modelValue":e[6]||(e[6]=o=>y.value.birthday=o)},null,512),[[E,y.value.birthday]])]),e[33]||(e[33]=t("div",{class:"col-span-2"},[t("button",{type:"submit",class:"bg-black text-white w-full mt-4"},"確認修改")],-1))],32)],512),[[q,k.value=="userData"]]),D(t("div",null,[e[41]||(e[41]=t("div",{class:"flex justify-between items-center mb-4"},[t("h3",{class:"text-xl font-bold"},"我的留言")],-1)),U.value?(n(),r("div",dt,[d(J),e[34]||(e[34]=t("p",{class:"mt-2 text-gray-500"},"載入留言中...",-1))])):C.value.length===0?(n(),r("div",ut,[e[36]||(e[36]=t("p",{class:"text-gray-500 mb-4"},"您還沒有任何留言",-1)),t("button",{class:"bg-blue-500 text-white hover:bg-blue-600 font-medium py-2 px-6 rounded transition-colors",onClick:me},e[35]||(e[35]=[t("i",{class:"fas fa-plus mr-1"},null,-1),c(" 新增留言 ")]))])):(n(),r("div",it,[t("div",ct,[(n(!0),r(z,null,K(ye.value,o=>(n(),r("div",{key:o.id,class:"mb-4"},[o.type==="user"?(n(),r("div",pt,[t("div",vt,[t("div",mt,[o.productId?(n(),r(z,{key:0},[c(" 商品留言："+i(o.product||"未知商品"),1)],64)):(n(),r(z,{key:1},[c(" 一般留言 ")],64))]),t("div",gt,i(o.content),1),t("div",ft,i(M(o.time)),1)]),e[37]||(e[37]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-circle text-3xl text-blue-400"})],-1))])):(n(),r("div",yt,[e[39]||(e[39]=t("div",{class:"flex-shrink-0"},[t("i",{class:"fas fa-user-shield text-3xl text-gray-400"})],-1)),t("div",xt,[t("div",_t,[o.productId?(n(),r(z,{key:0},[c(" 商品留言："+i(o.product||"未知商品"),1)],64)):(n(),r(z,{key:1},[c(" 一般留言 ")],64))]),t("div",ht,i(o.content),1),e[38]||(e[38]=t("div",{class:"text-xs text-gray-400 mt-1"},"管理員回覆",-1))])]))]))),128)),e[40]||(e[40]=t("div",{class:"h-16"},null,-1))]),t("div",bt,[t("div",wt,[D(t("input",{"onUpdate:modelValue":e[8]||(e[8]=o=>$.value=o),type:"text",class:"border rounded-l px-4 py-2 w-full",placeholder:"輸入留言內容...",onKeyup:Ae(ne,["enter"])},null,544),[[E,$.value]]),t("button",{class:"bg-blue-500 text-white px-4 py-2 rounded-r hover:bg-blue-600",onClick:ne}," 發送 ")])])]))],512),[[q,k.value=="comments"]])]),D(t("div",kt,[v.value&&v.value.length>0?(n(),r("div",Dt,[d(re,{current:u.current,"onUpdate:current":e[9]||(e[9]=o=>u.current=o),"page-size":u.pageSize,"onUpdate:pageSize":e[10]||(e[10]=o=>u.pageSize=o),total:u.total,"show-size-changer":u.showSizeChanger,"show-quick-jumper":u.showQuickJumper,"page-size-options":u.pageSizeOptions,"show-total":u.showTotal,onChange:X,onShowSizeChange:ee,size:"default"},null,8,["current","page-size","total","show-size-changer","show-quick-jumper","page-size-options","show-total"])])):_("",!0),v.value&&v.value.length>0?(n(),r("div",Ct,[d(re,{current:u.current,"onUpdate:current":e[11]||(e[11]=o=>u.current=o),"page-size":u.pageSize,"onUpdate:pageSize":e[12]||(e[12]=o=>u.pageSize=o),total:u.total,"show-size-changer":!1,"show-quick-jumper":!1,"show-total":u.showTotal,onChange:X,onShowSizeChange:ee,size:"small",simple:""},null,8,["current","page-size","total","show-total"])])):_("",!0)],512),[[q,k.value=="order"]]),d(V,{open:I.value,"onUpdate:open":e[15]||(e[15]=o=>I.value=o),title:"新增留言",maskClosable:!1,footer:null,width:500,bodyStyle:{padding:"20px"}},{default:p(()=>[e[42]||(e[42]=t("p",{class:"mb-2"},"請輸入您的留言：",-1)),d(xe,{value:T.value,"onUpdate:value":e[13]||(e[13]=o=>T.value=o),placeholder:"請輸入留言內容...",rows:6,maxLength:500,showCount:""},null,8,["value"]),t("div",zt,[t("button",{class:"bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded",onClick:e[14]||(e[14]=o=>I.value=!1)}," 取消 "),t("button",{class:"bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded",onClick:ge,disabled:P.value},[P.value?(n(),r("span",St,"處理中...")):(n(),r("span",Tt,"提交留言"))],8,At)])]),_:1,__:[42]},8,["open"])])}}};export{Ot as default};
