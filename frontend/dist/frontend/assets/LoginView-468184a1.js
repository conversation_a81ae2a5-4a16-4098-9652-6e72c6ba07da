import{_ as D,q as L,s as T,r as S,a as $,b as j,v as o,x as I,y as M,e as m,f as g,g as l,z,B,C as f,j as v,D as N,t as q,u as V,E as w}from"./index-971af9ba.js";const A={class:"container m-auto flex justify-center items-center"},E={class:"bg-gray-200 p-10 rounded-lg flex flex-col items-center w-full lg:w-1/3"},J={class:"w-full flex justify-around my-4 rounded-3xl relative bg-gray-300"},O={key:0,class:"mb-4"},P={key:1,class:"mb-4"},F={class:"mb-4"},G={key:2,class:"mt-3 flex items-center"},H={type:"submit",class:"bg-black text-white w-full rounded-2xl mt-4"},K={key:0,class:"text-sm mt-4"},Q={key:1,class:"text-sm mt-2 p-3 bg-blue-50 rounded-lg"},W={__name:"LoginView",setup(X){const _=L(),{clientData:C,isUser:U,total:Y,carts:x}=T(_),c=S(!1),p=S(!1),e={account:"",password:"",name:"",phone:""};$(),(()=>{const u=localStorage.getItem("saved_account"),t=localStorage.getItem("saved_password");u&&(e.account=u,p.value=!0),t&&(e.password=t,p.value=!0)})();const y=j.throttle(async()=>{var u,t;if(c.value){if(!e.account){o.error("請輸入帳號");return}if(!e.password){o.error("請輸入密碼");return}if(e.password.length<6){o.error("密碼長度至少需要6個字符");return}const r={mail:e.account,password:e.password,name:e.name||"未命名用戶"};console.log("註冊資料:",JSON.stringify(r,null,2));try{const n=o.loading("註冊中...",0),s=await I({method:"post",url:M("users/register"),data:r,headers:{"Content-Type":"application/json"}}).finally(()=>{n()});if(console.log("註冊響應:",s.data),s.data&&s.data.success)o.success("註冊成功！請等待管理員審核通過後即可登入使用"),e.account="",e.password="",e.name="",c.value=!1;else{console.error("註冊失敗，但沒有錯誤狀態碼:",s.data);const a=((u=s.data)==null?void 0:u.message)||"註冊失敗，請稍後再試或嘗試其他帳號";a.includes("已存在")||a.includes("already exists")||a.includes("已被註冊")||a.includes("已使用")||a.includes("duplicate")||a.includes("taken")?o.error(`此帳號 ${e.account} 已被註冊使用，請使用其他帳號`):o.error(a)}}catch(n){console.error("註冊請求錯誤:",n);const s=n.response,a=s==null?void 0:s.data;if(console.log("錯誤詳情:",a),(s==null?void 0:s.status)===400){const d=(a==null?void 0:a.message)||(a==null?void 0:a.error)||"註冊失敗，請稍後再試";d.includes("已存在")||d.includes("already exists")||d.includes("已被註冊")||d.includes("已使用")||d.includes("duplicate")||d.includes("taken")?o.error(`此帳號 ${e.account} 已被註冊使用，請使用其他帳號`):o.error(d)}else(s==null?void 0:s.status)===409?o.error(`此帳號 ${e.account} 已被註冊使用，請使用其他帳號`):o.error("註冊失敗，請稍後再試或嘗試其他帳號")}}else try{if(!e.account||!e.password){o.error("請輸入帳號和密碼");return}const r=o.loading("登入中...",0),n={mail:e.account,password:e.password},s=await I.post(M("users/login"),n).finally(()=>{r()});if(console.log("res",s.data),s.data&&s.data.success){if(o.success("登入成功"),localStorage.setItem("isLogin",!0),localStorage.setItem("token",s.data.body.token),p.value){localStorage.setItem("saved_account",e.account),localStorage.setItem("saved_password",e.password);const i=new Date;i.setDate(i.getDate()+30);const h=i.getTime();localStorage.setItem("saved_token",s.data.body.token),localStorage.setItem("token_expiry",h.toString())}else localStorage.removeItem("saved_account"),localStorage.removeItem("saved_password"),localStorage.removeItem("saved_token"),localStorage.removeItem("token_expiry");C.value=s.data.body,U.value=!0,o.success("登入成功");const a=localStorage.getItem("pending_purchase_item"),d=localStorage.getItem("checkout_after_login");if(a&&d==="true")try{const i=JSON.parse(a);x.value.find(k=>k.id==i.id&&k.specs==i.specs)?o.info("此商品已在購物車中"):x.value.push({...i,count:1,total:1*i.price}),_.calcTotal(),localStorage.removeItem("pending_purchase_item"),localStorage.removeItem("checkout_after_login"),localStorage.removeItem("redirect_after_login"),window.location.href="/check";return}catch(i){console.error("處理待購商品時發生錯誤:",i)}const b=localStorage.getItem("redirect_after_login");b?(localStorage.removeItem("redirect_after_login"),window.location.href=b):window.location.href="/"}else{const a=((t=s.data)==null?void 0:t.message)||"登入失敗";a.includes("尚未通過審核")||a.includes("審核")?o.error("您的帳號尚未通過審核，請聯繫管理員或稍後再試"):o.error(a)}}catch(r){console.error("登入錯誤:",r);const n=r.response,s=n==null?void 0:n.data;if((n==null?void 0:n.status)===403){const a=(s==null?void 0:s.message)||"帳號尚未通過審核";a.includes("尚未通過審核")||a.includes("審核")?o.error("您的帳號尚未通過審核，請聯繫管理員或稍後再試"):o.error(a)}else(n==null?void 0:n.status)===401?o.error("帳號或密碼錯誤，請檢查後重試"):o.error("登入失敗，請稍後再試")}},2e3,{trailing:!1});return(u,t)=>(m(),g("div",A,[l("div",E,[t[10]||(t[10]=l("div",{class:"text-center"},[l("h2",{class:"font-bold text-2xl"},"歡迎"),l("h3",null,"註冊或登入以繼續")],-1)),l("div",J,[l("button",{type:"button",class:"z-10 w-1/2 text-center",onClick:t[0]||(t[0]=r=>c.value=!0)}," 註冊會員 "),l("button",{type:"button",class:"z-10 w-1/2 text-center",onClick:t[1]||(t[1]=r=>c.value=!1)},"登入"),l("div",{class:z(["bg-white w-1/2 h-full absolute rounded-3xl shadow-md duration-200",c.value?"-translate-x-1/2":"translate-x-1/2"])},null,2)]),l("form",{onSubmit:t[7]||(t[7]=B((...r)=>V(y)&&V(y)(...r),["prevent"])),class:"w-full"},[c.value?(m(),g("div",O,[f(l("input",{class:"w-full",type:"text",placeholder:"請輸入姓名","onUpdate:modelValue":t[2]||(t[2]=r=>e.name=r)},null,512),[[w,e.name]])])):v("",!0),c.value?(m(),g("div",P,[f(l("input",{class:"w-full",type:"text",placeholder:"請輸入電話","onUpdate:modelValue":t[3]||(t[3]=r=>e.phone=r)},null,512),[[w,e.phone]])])):v("",!0),l("div",F,[f(l("input",{class:"w-full",type:"text",placeholder:"請輸入帳號","onUpdate:modelValue":t[4]||(t[4]=r=>e.account=r),required:""},null,512),[[w,e.account]])]),l("div",null,[f(l("input",{class:"w-full",type:"password",placeholder:"請輸入密碼","onUpdate:modelValue":t[5]||(t[5]=r=>e.password=r),required:""},null,512),[[w,e.password]])]),c.value?v("",!0):(m(),g("div",G,[f(l("input",{type:"checkbox",id:"rememberMe","onUpdate:modelValue":t[6]||(t[6]=r=>p.value=r),class:"mr-2"},null,512),[[N,p.value]]),t[8]||(t[8]=l("label",{for:"rememberMe",class:"text-sm"},"記住我的帳號",-1))])),l("button",H,q(c.value?"註冊":"登入"),1)],32),c.value?(m(),g("p",K," 一旦點擊註冊，即表示你同意中華民國儲蓄互助協會共購平台的 服務條款, 隱私政策 和 退款政策。 ")):v("",!0),c.value?(m(),g("div",Q,t[9]||(t[9]=[l("p",{class:"text-blue-700 font-medium mb-1"},"📋 註冊流程說明：",-1),l("p",{class:"text-blue-600"},"1. 提交註冊資料",-1),l("p",{class:"text-blue-600"},"2. 等待管理員審核（1-2個工作天）",-1),l("p",{class:"text-blue-600"},"3. 審核通過後即可正常登入使用",-1)]))):v("",!0)])]))}},ee=D(W,[["__scopeId","data-v-f1f4b778"]]);export{ee as default};
