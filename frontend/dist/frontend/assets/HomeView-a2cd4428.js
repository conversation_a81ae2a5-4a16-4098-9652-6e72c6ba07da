import{P as L,N,A as V,S as B,a as P}from"./autoplay-5e8a5d5d.js";import{_ as E,a as O,r as _,o as U,b as C,c as F,d as I,e as o,f as l,g as a,h as b,w as u,u as f,F as p,i as h,j as c,k as H,l as x,t as g,m as A,A as R}from"./index-971af9ba.js";import{P as W}from"./ProductItem-a77d35d0.js";const $=""+new URL("trending_flat-ef8e90ed.svg",import.meta.url).href,q=""+new URL("8339821-aa94f283.png",import.meta.url).href;const G={class:"carousel-container"},J=["href"],M=["src","alt"],z={key:0,class:"carousel-caption absolute bottom-0 left-0 w-full bg-black bg-opacity-50 text-white p-4"},K={key:0,class:"text-xl font-bold"},Q={key:1},T=["src","alt"],X={key:0,class:"carousel-caption absolute bottom-0 left-0 w-full bg-black bg-opacity-50 text-white p-4"},Y={key:0,class:"text-xl font-bold"},Z={key:1},tt={class:"container m-auto px-4 py-6 md:p-10 xl:p-0"},et={class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-10 lg:gap-20"},st={class:"mt-10 relative"},at={class:"text-center absolute top-1/2 -translate-x-1/2 left-1/2 -translate-y-1/2"},ot={class:"bg-white font-bold mt-2 md:mt-4 p-1 md:p-2 px-4 md:px-8 text-sm md:text-base"},lt={key:0,class:"bg-gray-100 w-full py-6 md:py-10"},rt={class:"container m-auto px-4"},nt={class:"md:hidden overflow-x-auto pb-4"},it={class:"flex space-x-4 px-2"},ct=["src","alt"],dt={class:"hidden md:flex items-center justify-center flex-wrap gap-4 xl:gap-6"},ut=["src","alt"],pt={__name:"HomeView",setup(gt){const{getApiData:y}=O(),m=_([]),d=_([]),i=_([]),k=t=>{console.error("品牌 Logo 載入失敗:",t.target.src),t.target.src=R.images.placeholder,t.target.style.objectFit="contain"},v=_(!1);U(async()=>{v.value=!!localStorage.getItem("isLogin");try{const t=await y("basic/carousel");t&&t.success?Array.isArray(t.data)?i.value=t.data:Array.isArray(t.resData)?i.value=t.resData:(console.warn("首頁輪播圖數據格式不符合預期:",t),i.value=[]):(console.warn("首頁無法獲取輪播圖或資料為空",t),i.value=[])}catch(t){console.error("首頁載入輪播圖時發生錯誤:",t),i.value=[]}try{const t=await y("product",{sort:"特色商品"});t&&t.resData&&Array.isArray(t.resData)?m.value=C.map(t.resData,s=>{let r=[];try{if(s.images){if(Array.isArray(s.images))r=s.images;else if(typeof s.images=="string"){const n=s.images.trim();n.startsWith("[")&&n.endsWith("]")?r=JSON.parse(n):r=[n]}}}catch(n){console.error("解析商品圖片時出錯:",n,"商品ID:",s.id,"原始圖片數據:",s.images),typeof s.images=="string"&&s.images.trim()!==""?r=[s.images.trim()]:r=[]}return{...s,images:r}}).splice(0,20):(console.warn("首頁無法獲取特色商品或資料為空"),m.value=[])}catch(t){console.error("首頁載入特色商品時發生錯誤:",t),m.value=[]}try{const t=await y("brands");t&&t.success&&Array.isArray(t.resData)?d.value=t.resData.slice(0,5):(console.warn("首頁無法獲取品牌列表或資料為空:",t==null?void 0:t.message),d.value=[])}catch(t){console.error("首頁載入品牌列表時發生錯誤:",t),d.value=[]}});const D=Object.values(Object.assign({})).map(t=>({url:t})),j=F(()=>i.value&&i.value.length>0?i.value.map(t=>{let s=t.image_url;return s&&(s=H(s)),{url:s,title:t.title||"",description:t.description||"",link:t.link||""}}):D),S=[L,N,V];return(t,s)=>{const r=I("RouterLink"),n=I("router-link");return o(),l(p,null,[a("div",G,[b(f(P),{slidesPerView:1,spaceBetween:30,loop:!0,pagination:{clickable:!0},navigation:!0,modules:S,class:"mySwiper",autoplay:{delay:5e3,disableOnInteraction:!1}},{default:u(()=>[(o(!0),l(p,null,h(j.value,(e,w)=>(o(),x(f(B),{key:w},{default:u(()=>[e.link?(o(),l("a",{key:0,href:e.link,target:"_blank"},[a("img",{src:e.url,alt:e.title||"首頁輪播圖",class:"carousel-image"},null,8,M),e.title||e.description?(o(),l("div",z,[e.title?(o(),l("h3",K,g(e.title),1)):c("",!0),e.description?(o(),l("p",Q,g(e.description),1)):c("",!0)])):c("",!0)],8,J)):(o(),l(p,{key:1},[a("img",{src:e.url,alt:e.title||"首頁輪播圖",class:"carousel-image"},null,8,T),e.title||e.description?(o(),l("div",X,[e.title?(o(),l("h3",Y,g(e.title),1)):c("",!0),e.description?(o(),l("p",Z,g(e.description),1)):c("",!0)])):c("",!0)],64))]),_:2},1024))),128))]),_:1})]),a("main",tt,[s[1]||(s[1]=a("h2",{class:"text-center my-10 text-4xl font-bold"},"特色商品",-1)),a("div",et,[(o(!0),l(p,null,h(m.value,(e,w)=>(o(),x(W,{key:w,productData:e,keyWord:"特色商品"},null,8,["productData"]))),128))]),b(r,{to:{path:"/products",query:{sort:"特色商品"}}},{default:u(()=>s[0]||(s[0]=[a("div",{class:"flex justify-center mt-20"},[a("p",null,"查看更多特色商品"),a("img",{src:$,class:"w-8",alt:"trending_flat"})],-1)])),_:1,__:[0]})]),a("div",st,[s[4]||(s[4]=a("img",{src:q,class:"w-full",alt:"menu"},null,-1)),a("div",at,[s[2]||(s[2]=a("h4",{class:"text-xl sm:text-2xl md:text-4xl font-bold mb-2 md:mb-4 text-white"}," 中華民國儲蓄互助協會共購平台 ",-1)),s[3]||(s[3]=a("p",{class:"text-sm sm:text-base md:text-2xl text-white"},"服務 | 互助 | 共購",-1)),b(r,{to:v.value?"/account":"/login",class:"block"},{default:u(()=>[a("button",ot,g(v.value?"前往個人中心":"立即加入會員"),1)]),_:1},8,["to"])])]),d.value.length>0?(o(),l("div",lt,[a("div",rt,[s[5]||(s[5]=a("h3",{class:"text-center text-xl md:text-2xl font-bold mb-4 md:mb-6"},"合作品牌",-1)),a("div",nt,[a("div",it,[(o(!0),l(p,null,h(d.value,e=>(o(),x(n,{key:e.id,to:`/brand/${e.id}`,class:"bg-white p-2 rounded-lg flex-shrink-0 flex items-center justify-center shadow hover:shadow-md transition-shadow duration-300",style:{width:"140px",height:"60px"}},{default:u(()=>[a("img",{src:f(A)(e.logo,"brand"),alt:e.name,class:"max-h-full max-w-full object-contain",onError:k},null,40,ct)]),_:2},1032,["to"]))),128))])]),a("div",dt,[(o(!0),l(p,null,h(d.value,e=>(o(),x(n,{key:e.id,to:`/brand/${e.id}`,class:"bg-white p-3 rounded-lg flex items-center justify-center shadow hover:shadow-md transition-shadow duration-300",style:{width:"180px",height:"80px"}},{default:u(()=>[a("img",{src:f(A)(e.logo,"brand"),alt:e.name,class:"max-h-full max-w-full object-contain",onError:k},null,40,ut)]),_:2},1032,["to"]))),128))])])])):c("",!0)],64)}}},xt=E(pt,[["__scopeId","data-v-f46e282d"]]);export{xt as default};
